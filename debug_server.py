#!/usr/bin/env python3
"""
Debug server script with connection monitoring and better error handling.
Use this for debugging connection issues instead of the basic uvicorn command.
"""

import asyncio
import logging
import os
import signal
import sys
import time

from uvicorn.config import Config
from uvicorn.server import Server

# Add the src/aica_agent directory to Python path so we can import the application
current_dir = os.path.dirname(os.path.abspath(__file__))
aica_agent_dir = os.path.join(current_dir, "src", "aica_agent")
if aica_agent_dir not in sys.path:
    sys.path.insert(0, aica_agent_dir)

# Configure logging for debugging
logging.basicConfig(
    level=logging.DEBUG, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

logger = logging.getLogger(__name__)

# Global connection tracking
connection_tracker = {
    'active_connections': set(),
    'connection_count': 0
}


class DebugServer(Server):
    """Enhanced server with connection monitoring"""

    def __init__(self, config: Config):
        super().__init__(config)
        self.start_time = time.time()

        # Override the protocol factory to track connections
        original_protocol_factory = config.http_protocol_class

        def connection_tracking_protocol_factory(*args, **kwargs):
            protocol = original_protocol_factory(*args, **kwargs)

            # Wrap the connection_made and connection_lost methods
            original_connection_made = protocol.connection_made
            original_connection_lost = protocol.connection_lost

            def connection_made(transport):
                connection_id = f"{transport.get_extra_info('peername')}"
                connection_tracker['active_connections'].add(connection_id)
                connection_tracker['connection_count'] += 1
                logger.debug(f"New connection: {connection_id}")
                # Store transport for later reference
                protocol._transport = transport
                return original_connection_made(transport)

            def connection_lost(exc):
                if hasattr(protocol, '_transport') and protocol._transport:
                    connection_id = f"{protocol._transport.get_extra_info('peername')}"
                    connection_tracker['active_connections'].discard(connection_id)
                    logger.debug(f"Connection lost: {connection_id}")
                return original_connection_lost(exc)

            protocol.connection_made = connection_made
            protocol.connection_lost = connection_lost
            return protocol

        config.http_protocol_class = connection_tracking_protocol_factory

    async def startup(self, sockets=None):
        """Enhanced startup with monitoring"""
        logger.info("Starting debug server with connection monitoring...")
        await super().startup(sockets)

        # Start connection monitoring task
        asyncio.create_task(self.monitor_connections())

    async def monitor_connections(self):
        """Monitor active connections and log statistics"""
        while True:
            try:
                uptime = time.time() - self.start_time
                logger.info(
                    f"Server Stats - Uptime: {uptime:.1f}s, "
                    f"Active Connections: {len(connection_tracker['active_connections'])}, "
                    f"Total Connections: {connection_tracker['connection_count']}"
                )
                await asyncio.sleep(10)  # Log every 10 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(
                    f"Error in connection monitor: {e}",
                    exc_info=True,
                )
                await asyncio.sleep(10)


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    del frame  # Unused parameter
    logger.info(f"Received signal {signum}, shutting down gracefully...")
    sys.exit(0)


def main():
    """Main function to run the debug server"""
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Store the original working directory for reload watching
    original_cwd = os.getcwd()

    # Change to the aica_agent directory for proper imports
    aica_agent_dir = os.path.join(original_cwd, "src", "aica_agent")
    if os.path.exists(aica_agent_dir):
        os.chdir(aica_agent_dir)
        logger.info(f"Changed working directory to: {aica_agent_dir}")
    else:
        logger.error(f"Directory not found: {aica_agent_dir}")
        sys.exit(1)

    # Configure uvicorn with debug-friendly settings
    # Watch both the current directory and the project root for changes
    reload_dirs = [
        "./",  # Current directory (src/aica_agent)
        original_cwd,  # Project root directory
        os.path.join(original_cwd, "src"),  # src directory
    ]

    config = Config(
        app="application:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        reload_dirs=reload_dirs,
        reload_includes=["*.py", "*.yml"],
        log_level="debug",
        access_log=True,
        # Connection settings optimized for debugging
        limit_concurrency=50,  # Lower limit for easier debugging
        timeout_keep_alive=30,
        timeout_graceful_shutdown=30,
        # Enable detailed logging
        use_colors=True,
    )

    # Create and run the debug server
    server = DebugServer(config)

    logger.info("Starting AICA Agent Debug Server...")
    logger.info("Configuration:")
    logger.info(f"  - Host: {config.host}:{config.port}")
    logger.info(f"  - Reload: {config.reload}")
    logger.info(f"  - Reload Directories: {config.reload_dirs}")
    logger.info(f"  - Reload Includes: {config.reload_includes}")
    logger.info(f"  - Concurrency Limit: {config.limit_concurrency}")
    logger.info(f"  - Keep-Alive Timeout: {config.timeout_keep_alive}s")

    try:
        server.run()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
