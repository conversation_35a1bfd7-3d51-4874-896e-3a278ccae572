{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Agent",
            "type": "debugpy",
            "request": "launch",
            "module": "uvicorn",
            "cwd": "${workspaceFolder}/src/aica_agent",
            "args": [
                "application:app",
                "--reload",
                "--host", "0.0.0.0",
                "--port", "8000",
                "--limit-concurrency", "100",
                "--timeout-keep-alive", "30"
            ],
            "envFile": "${workspaceFolder}/.env.local",
        },
        {
            "name": "Agent (Production-like)",
            "type": "debugpy",
            "request": "launch",
            "module": "gunicorn",
            "cwd": "${workspaceFolder}/src/aica_agent",
            "args": [
                "-k", "uvicorn.workers.UvicornWorker",
                "--worker-connections", "100",
                "--max-requests", "100",
                "--max-requests-jitter", "10",
                "--timeout", "300",
                "--bind", "0.0.0.0:8000",
                "application:app"
            ],
            "envFile": "${workspaceFolder}/.env.local",
            "env": {
                "OBJC_DISABLE_INITIALIZE_FORK_SAFETY": "YES"
            }
        },
        {
            "name": "Agent (Debug with Monitoring)",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/debug_server.py",
            "cwd": "${workspaceFolder}",
            "envFile": "${workspaceFolder}/.env.local",
            "console": "integratedTerminal"
        },
    ]
}
